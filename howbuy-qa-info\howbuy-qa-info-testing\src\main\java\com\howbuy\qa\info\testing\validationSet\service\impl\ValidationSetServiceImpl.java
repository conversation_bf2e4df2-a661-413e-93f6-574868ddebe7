package com.howbuy.qa.info.testing.validationSet.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.howbuy.qa.api.dto.TableColumnInfoDTO;
import com.howbuy.qa.api.external.platform.service.PlatformConfigApi;
import com.howbuy.qa.api.external.platform.utils.PlatformRest;
import com.howbuy.qa.info.testing.utils.DateUtils;
import com.howbuy.qa.info.testing.validationSet.bo.ValidationSetPivotTableBO;
import com.howbuy.qa.info.testing.validationSet.dto.ValidationSetPivotTableDTO;
import com.howbuy.qa.info.testing.validationSet.mapper.ValidationSetMapper;
import com.howbuy.qa.info.testing.validationSet.po.ValidationSetDataPO;
import com.howbuy.qa.info.testing.validationSet.po.ValidationSetPO;
import com.howbuy.qa.info.testing.validationSet.service.BatchOperateValidationSetDataStrategyFactory;
import com.howbuy.qa.info.testing.validationSet.service.ValidationSetDataService;
import com.howbuy.qa.info.testing.validationSet.service.ValidationSetService;
import com.howbuy.qa.info.testing.validationSet.vo.BatchOperateValidationSetDataVO;
import com.howbuy.qa.info.testing.validationSet.vo.BatchUpdateTableNameVO;
import com.howbuy.qa.info.testing.validationSet.vo.CopyValidationSetVO;
import com.howbuy.qa.info.testing.validationSet.vo.ValidationSetQueryVO;
import com.howbuy.utils.page.PageData;
import com.howbuy.utils.page.PageParam;
import com.howbuy.utils.page.PageUtils;
import com.howbuy.utils.UserContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.howbuy.qa.info.testing.validationSet.constant.ValidationConstants.VIRTUAL_VERSION;
import static com.howbuy.utils.MapStructUtils.mapStructObject;

@Slf4j
@Service
public class ValidationSetServiceImpl extends ServiceImpl<ValidationSetMapper, ValidationSetPO> implements ValidationSetService {

    @Resource
    ValidationSetDataService validationSetDataService;

    @Resource
    PlatformConfigApi platformConfigApi;


    @Override
    public PageData<ValidationSetPO> getValidationSetByPage(PageParam<ValidationSetQueryVO> vo) {
        LambdaQueryWrapper<ValidationSetPO> qw = new LambdaQueryWrapper<>();
        if (vo.getData() != null) {
            if (StringUtils.isNotBlank(vo.getData().getAppName())) {
                qw.eq(ValidationSetPO::getAppName, vo.getData().getAppName());
            }
            if (StringUtils.isNotBlank(vo.getData().getDbAlias())) {
                qw.eq(ValidationSetPO::getDbAlias, vo.getData().getDbAlias());
            }
            if (StringUtils.isNotBlank(vo.getData().getTableName())) {
                qw.eq(ValidationSetPO::getTableName, vo.getData().getTableName());
            }
            if (StringUtils.isNotBlank(vo.getData().getValidationSetName())) {
                qw.like(ValidationSetPO::getValidationSetName, vo.getData().getValidationSetName());
            }
        }
        qw.orderByDesc(ValidationSetPO::getId);
        Page<ValidationSetPO> page = PageUtils.getPage(vo);
        this.getBaseMapper().selectPage(page, qw);
        return PageUtils.getPageData(page.getRecords(), page);
//        Long total = this.baseMapper.countValidationSet(vo);
//        if (vo.getPageSize() == null) {
//            vo.setPageSize(total.intValue());
//        }
//        List<ValidationSetPO> bos =  this.baseMapper.selectValidationSet(vo);
//        PageData<ValidationSetPO> pageData = PageUtils.getPageData(bos, vo.getPageNum(), vo.getPageSize(), total);
//        return pageData;
    }

    @Override
    public ValidationSetPO getValidationById(Long id) {
        return this.getById(id);
    }

    @Override
    public List<ValidationSetPO> getTableInfo(ValidationSetQueryVO vo) {
        QueryWrapper<ValidationSetPO> qw = new QueryWrapper<>();
        qw.select("DISTINCT table_name,app_name,db_alias").lambda();
        if (StringUtils.isNotBlank(vo.getAppName())) {
            qw.eq("app_name", vo.getAppName());
        }
        if (StringUtils.isNotBlank(vo.getDbAlias())) {
            qw.eq("db_alias", vo.getDbAlias());
        }
        return this.list(qw);
    }

    @Override
    public void validationSetUpdateById(ValidationSetPO validationSetPO) {
        this.updateById(validationSetPO);
    }

    @Override
    public void validationBatchUpdate(BatchUpdateTableNameVO batchUpdateTableNameVO) {
        if (Objects.nonNull(batchUpdateTableNameVO) &&
            StringUtils.isNotBlank(batchUpdateTableNameVO.getAppName()) &&
            StringUtils.isNotBlank(batchUpdateTableNameVO.getDbAlias())) {

            // 使用条件查询，模仿BatchOperateValidationSetDataVO的查询方式
            BatchOperateValidationSetDataVO queryVO = new BatchOperateValidationSetDataVO();
            queryVO.setAppName(batchUpdateTableNameVO.getAppName());
            queryVO.setDbAlias(batchUpdateTableNameVO.getDbAlias());
            queryVO.setCaseVersion(batchUpdateTableNameVO.getCaseVersion());
            queryVO.setCaseAppName(batchUpdateTableNameVO.getCaseAppName());

            // 查询符合条件的验证集
            List<ValidationSetPO> targetValidationSets = this.baseMapper.queryByAppNameAndDbAndTableAndVersion(queryVO);

            if (CollectionUtils.isNotEmpty(targetValidationSets)) {
                List<ValidationSetPO> validationSetPOS = new ArrayList<>();
                for (ValidationSetPO validationSet : targetValidationSets) {
                    ValidationSetPO updatePO = new ValidationSetPO();
                    updatePO.setTableName(batchUpdateTableNameVO.getTableName());
                    updatePO.setId(validationSet.getId());
                    updatePO.setUpdater((String) UserContext.getUser());
                    updatePO.setUpdateTime(new Date());
                    validationSetPOS.add(updatePO);
                }
                this.updateBatchById(validationSetPOS);
            }
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public ValidationSetPO saveOrUpdate(ValidationSetQueryVO vo) {
        ValidationSetPO po = mapStructObject(vo, ValidationSetPO.class);
        if (Objects.isNull(vo.getId())) {
            this.checkValidationSetName(vo.getValidationSetName(), vo.getDbAlias(), vo.getTableName(), vo.getCaseVersion());
            if (StringUtils.isBlank(po.getCaseVersion())) {
                po.setCaseVersion(VIRTUAL_VERSION);
            }
            if (StringUtils.isBlank(po.getDataBatch())) {
                po.setDataBatch(DateUtils.getNowDateString());
            }
            this.save(po);
        } else {
            if (StringUtils.isNotBlank(vo.getValidationSetName())) {
                ValidationSetPO validationSetPO = this.getById(vo.getId());
                if (!validationSetPO.getValidationSetName().equalsIgnoreCase(vo.getValidationSetName())) {
                    this.checkValidationSetName(vo.getValidationSetName(), vo.getDbAlias(), vo.getTableName(), vo.getCaseVersion());
                }
            }
            this.updateById(po);
        }
        return po;

    }

    @Override
    public Boolean deleteByValidationSetId(Long validationSetId) {
        return this.removeById(validationSetId);
    }

    @Override
    public ValidationSetPivotTableDTO getTableValidationSetData(ValidationSetQueryVO vo) {
        ValidationSetPivotTableDTO validationSetPivotTableDTO = new ValidationSetPivotTableDTO();
        List<String> tableColumn = this.baseMapper.selectValidationSetTableColumn(vo);
        if (CollectionUtils.isNotEmpty(tableColumn)) {
            List<ValidationSetPivotTableBO> validationSetPivotTable = this.baseMapper.selectTableValidationSetData(vo);
            List<ValidationSetPivotTableBO> bos = validationSetPivotTable.stream().filter(item -> Objects.nonNull(item.getValidationSetData())).collect(Collectors.toList());
            List<Map<String, String>> tableData = new ArrayList<>();
            Map<String, Integer> mergeCellTable = new HashMap<>();
            Map<Integer, Integer> mergeCellRow = new HashMap<>();
            for (int i = 0; i < bos.size(); i++) {

                try {
                    Map<String, String> rowData = JSONObject.parseObject(bos.get(i).getValidationSetData(), Map.class);
                    if (mergeCellTable.containsKey(rowData.get("validation_set_id"))) {
                        mergeCellRow.put(i - mergeCellTable.get(rowData.get("validation_set_id")), mergeCellTable.get(rowData.get("validation_set_id")) + 1);
                        mergeCellTable.put(rowData.get("validation_set_id"), mergeCellTable.get(rowData.get("validation_set_id")) + 1);
                    } else {
                        mergeCellTable.put(rowData.get("validation_set_id"), 1);
                        mergeCellRow.put(i, 1);
                    }
                    tableData.add(rowData);
                } catch (Exception e) {
                    log.error(bos.get(i).getValidationSetName() + i, e);
                    throw new RuntimeException("数据格式错误:" + bos.get(i).getValidationSetName());
                }


            }
            validationSetPivotTableDTO.setValidationSetPivotTable(bos);
            validationSetPivotTableDTO.setTableData(tableData);
            validationSetPivotTableDTO.setMergeCellTable(mergeCellTable);
            validationSetPivotTableDTO.setMergeCellRow(mergeCellRow);
        }
        validationSetPivotTableDTO.setTableColumn(tableColumn);
        return validationSetPivotTableDTO;
    }

    @Override
    public ValidationSetPO copyValidationSet(CopyValidationSetVO vo) {
        ValidationSetPO validationSetPO = this.getById(vo.getCopyValidationSetId());
        this.checkValidationSetName(vo.getValidationSetName(), validationSetPO.getDbAlias(), validationSetPO.getTableName(), validationSetPO.getCaseVersion());
        validationSetPO.setValidationSetName(vo.getValidationSetName());
        validationSetPO.setId(null);
        this.saveOrUpdate(validationSetPO);
        return validationSetPO;
    }

    @Override
    public ValidationSetPO getValidationSetOnCaseVersion(ValidationSetPO validationSetPO) {
        LambdaQueryWrapper<ValidationSetPO> qw = new LambdaQueryWrapper<>();

        qw.eq(ValidationSetPO::getAppName, validationSetPO.getAppName());
        qw.eq(ValidationSetPO::getDbAlias, validationSetPO.getDbAlias());
        qw.eq(ValidationSetPO::getValidationSetName, validationSetPO.getValidationSetName());
        qw.eq(ValidationSetPO::getTableName, validationSetPO.getTableName());
        qw.eq(ValidationSetPO::getCaseVersion, validationSetPO.getCaseVersion());
        return this.baseMapper.selectOne(qw);
    }

    @Override
    public List<ValidationSetPO> querySelectableValidationSet(ValidationSetQueryVO vo) {
        List<String> versions = Lists.newArrayList(vo.getCaseVersion(), VIRTUAL_VERSION);
        return this.baseMapper.querySelectableValidationSet(vo, versions);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean batchOperateValidationSetData(BatchOperateValidationSetDataVO vo) {
        List<ValidationSetPO> validationSetPOS = this.baseMapper.queryByAppNameAndDbAndTableAndVersion(vo);
        if (CollectionUtils.isNotEmpty(validationSetPOS)) {
            validationSetPOS.stream().forEach(item -> {
                validationSetDataService.checkDataBatchHasManyValidationSet(item.getDataBatch(), vo.getCaseVersion());
            });
        }
        Boolean res = BatchOperateValidationSetDataStrategyFactory.getStrategy(vo.getOperationType()).batchOperateValidationSetData(vo);
        return res;
    }

    private void checkValidationSetName(String name, String dbAlias, String tableName, String caseVersion) {
        LambdaQueryWrapper<ValidationSetPO> qw = new LambdaQueryWrapper<>();
        qw.eq(ValidationSetPO::getValidationSetName, name);
        qw.eq(ValidationSetPO::getDbAlias, dbAlias);
        qw.eq(ValidationSetPO::getTableName, tableName);
        qw.eq(ValidationSetPO::getCaseVersion, caseVersion);
        if (this.baseMapper.selectCount(qw) > 0) {
            log.error("验证集名称已存在:{},{},{},{}", name, dbAlias, tableName, caseVersion);
            throw new RuntimeException("验证集名称已存在");
        }
    }
}

