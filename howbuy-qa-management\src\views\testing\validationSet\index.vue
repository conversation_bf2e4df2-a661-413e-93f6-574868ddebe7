<template>
  <div>
    <el-container>
      <el-header style="font-size: 12px; margin-top: 10px" height="40px">
        <ValidationSetFilter :form="paramForm" @click="onSubmit" />
      </el-header>
      <el-main>
        <div style="margin-bottom: 10px;">
          <el-button
            type="primary"
            :disabled="selectedRows.length === 0"
            @click="showBatchUpdateTableName"
          >
            批量修改表名 ({{ selectedRows.length }})
          </el-button>
        </div>
        <hr />
        <ValidationSetTable
          ref="validationSetTable"
          :page="page"
          @selection-change="handleSelectionChange"
        />
      </el-main>

      <el-footer>
        <el-pagination
          :hide-on-single-page="false"
          v-model:current-page="page.pageNum"
          :page-sizes="[5, 10, 20, 50, 100]"
          :page-size="page.pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="page.total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </el-footer>
    </el-container>

    <!-- 批量修改表名对话框 -->
    <BatchModifyValidation
      ref="batchModifyValidation"
      @refresh="refreshList"
    />
  </div>
</template>

<script>
import ValidationSetFilter from '@/views/testing/validationSet/components/ValidationSetFilter'
import ValidationSetTable from '@/views/testing/validationSet/components/ValidationSetTable'
import BatchModifyValidation from '@/views/testing/validationSet/components/BatchModifyValidation'
export default {
  name: 'ValidationSetIndex',
  components: {
    ValidationSetTable,
    ValidationSetFilter,
    BatchModifyValidation,
  },
  data() {
    return {
      paramForm: {},
      defaultHeight: {
        height: 200,
      },
      page: {
        pageNum: 1,
        pageSize: 10,
        total: 0,
      },
      activeName: 'first',
      selectedRows: [], // 选中的行数据
    }
  },
  mounted() {},
  methods: {
    onSubmit(paramForm) {
      this.paramForm = paramForm || this.paramForm
      this.page.pageNum = 1
      this.page.pageSize = 10
      this.$refs.validationSetTable.query(this.paramForm)
    },
    handleSizeChange(val) {
      // console.log(`每页 ${val} 条`);
      this.page.pageSize = val
      this.$refs.testSetTable.query(this.paramForm)
    },
    handleCurrentChange(val) {
      // console.log(`当前页: ${val}`);
      this.page.pageNum = val
      this.$refs.validationSetTable.query(this.paramForm)
    },
    handleClick(tab, event) {
      console.log(tab, event)
    },
    showRunEnv(id) {
      this.$refs.selectEnvRunDialogRef.openSelectEnv(id)
    },
    handleSelectionChange(selection) {
      this.selectedRows = selection
    },
    showBatchUpdateTableName() {
      if (this.selectedRows.length === 0) {
        this.$message.warning('请先选择要修改的验证集')
        return
      }

      // 获取选中验证集的表名（假设所有选中的验证集都是同一个表名）
      const firstRow = this.selectedRows[0]
      const currentTableName = firstRow.tableName

      // 显示批量修改表名对话框，传递当前表名作为原表名
      this.$refs.batchModifyValidation.show({
        operationType: 'updateTableName',
        appName: firstRow.appName,
        validationAppName: firstRow.appName,
        dbAlias: firstRow.dbAlias,
        caseVersion: firstRow.caseVersion || '',
        oldTableName: currentTableName,  // 当前表名作为原表名
        selectedCount: this.selectedRows.length
      })
    },
    refreshList() {
      // 刷新列表
      this.$refs.validationSetTable.query(this.paramForm)
      // 清空选择
      this.selectedRows = []
    },
  },
}
</script>

<style></style>

<style scoped>
/*.el-header {*/
/*display:  flex;*/
/*justify-content: flex-start;*/
/*align-items: flex-start;*/
/*!*background-color: #fe9faa;*!*/
/*color: #c9f0cb;*/
/*line-height: 60px;*/
/*}*/
</style>
