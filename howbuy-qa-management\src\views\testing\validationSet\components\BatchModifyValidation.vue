<template>
  <el-dialog
    v-model="dialogVisible"
    :close-on-click-modal="false"
    width="40%"
    style="margin: auto"
    :before-close="close"
    :title="title"
    append-to-body
    center
  >
    <el-form
      ref="ruleForm"
      :model="ruleForm"
      :rules="rules"
      :label-position="labelPosition"
    >
      <el-form-item
        label="用例应用:"
        prop="appName"
        :label-width="formLabelWidth"
      >
        <el-col :span="6">
          <el-select
            v-model="ruleForm.appName"
            placeholder="请选择应用"
            clearable
            filterable
            style="width: 250px"
            size="small"
            @change="appNameChange"
          >
            <el-option
              v-for="item in apps"
              :key="item"
              :label="item"
              :value="item"
            />
          </el-select>
        </el-col>
      </el-form-item>
      <el-form-item
        label="分支版本:"
        prop="caseVersion"
        :label-width="formLabelWidth"
      >
        <el-select
          v-model="ruleForm.caseVersion"
          style="width: 250px"
          :clearable="true"
          :filterable="true"
          prop="caseVersion"
          placeholder="请选择版本！"
          size="small"
        >
          <el-option
            v-for="item in versionOptions"
            :key="item"
            :label="item"
            :value="item"
          />
        </el-select>
      </el-form-item>
      <el-form-item
        label="验证集应用:"
        prop="validationAppName"
        :label-width="formLabelWidth"
      >
        <el-col :span="6">
          <el-select
            v-model="ruleForm.validationAppName"
            placeholder="请选择应用"
            clearable
            filterable
            style="width: 250px"
            size="small"
            @change="validationAppNameChange"
          >
            <el-option
              v-for="item in apps"
              :key="item"
              :label="item"
              :value="item"
            />
          </el-select>
        </el-col>
      </el-form-item>
      <el-form-item
        label="数据库别名:"
        prop="dbAlias"
        :label-width="formLabelWidth"
      >
        <el-select
          v-model="ruleForm.dbAlias"
          size="small"
          style="width: 250px"
          filterable
          clearable
          placeholder="请选择"
          @change="dbAliasChange"
        >
          <el-option
            v-for="item in appAlias"
            :key="item + '_db'"
            :label="item"
            :value="item"
          />
        </el-select>
      </el-form-item>
      <el-form-item
        label="表名:"
        prop="tableName"
        :label-width="formLabelWidth"
      >
        <el-select
          v-model="ruleForm.tableName"
          size="small"
          style="width: 250px"
          filterable
          clearable
          :placeholder="ruleForm.operationType === 'updateTableName' ? '请选择新表名' : '请选择表名'"
          :span="6"
        >
          <el-option
            v-for="item in tableNameList"
            :key="item"
            :label="item"
            :value="item"
          />
        </el-select>
      </el-form-item>
      <el-form-item
        v-show="ruleForm.operationType !== 'updateTableName'"
        :label-width="formLabelWidth"
        label="字段名:"
        prop="columnName"
      >
        <el-input
          v-model="ruleForm.columnName"
          style="width: 250px"
          clearable
          placeholder="字段名"
          size="small"
        />
      </el-form-item>
      <el-form-item
        v-show="ruleForm.operationType == 'alertColumn'"
        :label-width="formLabelWidth"
        label="新字段名:"
        prop="newColName"
      >
        <el-input
          v-model="ruleForm.newColumnName"
          style="width: 250px"
          clearable
          placeholder="新字段名"
          size="small"
        />
      </el-form-item>
      <el-form-item
        v-show="
          ruleForm.operationType == 'updateValue' ||
          ruleForm.operationType == 'addColumn'
        "
        :label-width="formLabelWidth"
        label="字段值:"
        prop="colValue"
      >
        <el-input
          v-model="ruleForm.columnValue"
          style="width: 250px"
          clearable
          placeholder="字段值"
          size="small"
        />
      </el-form-item>
    </el-form>

    <template v-slot:footer>
      <div class="dialog-footer" align="right">
        <el-button size="small" @click="close('ruleForm')">取 消</el-button>
        <el-button type="primary" size="small" @click="ok('ruleForm')"
          >提 交</el-button
        >
      </div>
    </template>
  </el-dialog>
</template>

<script>
import { ElLoading } from 'element-plus'
import { queryTableInfoByAppEnvAndDBAlias } from '@/api/spider/appManage'
import {
  batchOperateValidationSetDataApi,
  batchUpdateTableNameApi
} from '@/api/howbuyQaInfo/testing/scriptManagement/scriptController'
import {
  findAllAppInfo,
  queryDbAliasByAppName,
  findAppVersionByAppName,
} from '@/api/spider/appManage'
export default {
  name: 'BatchModifyValidation',
  props: {
    // modifyValidationSet: { type: Function, defaults: null },
    operationType: String,
  },
  data() {
    return {
      labelPosition: 'right',
      onClickClose: false,
      dialogVisible: false,
      formLabelWidth: '120px',
      tableNameList: [],
      versionOptions: [],
      title: '',
      titleDict: {
        alertColumn: '批量修改字段名',
        updateValue: '批量更新字段值',
        deleteColumn: '批量删除字段',
        addColumn: '批量添加字段',
        updateTableName: '批量修改表名'
      },
      ruleForm: {
        validationSetName: '',
        appName: '',
        validationAppName: '',
        dbAlias: '',
        tableName: '',
        oldTableName: '',
        copyValidationSetId: 0,
        operationType: '',
        caseVersion: '',
        columnName: '',
        newColumnName: '',
        columnValue: ''
      },
      defaultRuleForm: {
        validationSetName: '',
        appName: '',
        dbAlias: '',
        tableName: '',
        oldTableName: '',
        copyValidationSetId: 0,
        operationType: '',
        caseVersion: '',
        columnName: '',
        newColumnName: '',
        columnValue: ''
      },
      apps: [],
      appAlias: [],
      rules: {
        // validationSetName: [
        //   { required: true, message: '请输入数据校验名称', trigger: 'blur' }
        // ],
        appName: [{ required: true, message: '请选择应用', trigger: 'change' }],
        caseVersion: [
          { required: true, message: '请输入分支号', trigger: 'change' },
        ],
        validationAppName: [
          { required: true, message: '请选择应用', trigger: 'change' },
        ],
        dbAlias: [
          { required: true, message: '请选择数据库别名', trigger: 'change' },
        ],
        tableName: [
          { required: true, message: '请输入表名', trigger: 'change' },
        ],
        columnName: [
          { required: true, message: '请输入字段名', trigger: 'blur' },
        ],
      },
    }
  },

  methods: {
    validationAppNameChange() {
      this.ruleForm.dbAlias = ''
      this.queryAppNameDBAlias()
      this.queryTableNameList()
    },
    appNameChange() {
      this.getVersionListByAppName(this.ruleForm.appName)
    },
    getVersionListByAppName(appName) {
      if (appName) {
        // 删除第二个select绑定的属性
        delete this.ruleForm.caseVersion
        findAppVersionByAppName({ appName: appName }).then((res) => {
          if (res.code === 200) {
            this.versionOptions = res.data
            // console.info(this.showMasterVersion)
            // if (this.showMasterVersion) {
            //   const index = this.versionOptions.indexOf('master')
            //   if (index < 0) {
            //     this.versionOptions.push('master')
            //   }
            // }
          } else {
            this.$message.error(res.message)
          }
        })
      }
    },
    queryTableNameList() {
      if (
        this.ruleForm.validationAppName !== '' &&
        this.ruleForm.validationAppName !== null &&
        this.ruleForm.validationAppName !== undefined &&
        this.ruleForm.dbAlias !== '' &&
        this.ruleForm.dbAlias !== null &&
        this.ruleForm.dbAlias !== undefined
      ) {
        queryTableInfoByAppEnvAndDBAlias({
          appName: this.ruleForm.validationAppName,
          dbAlias: this.ruleForm.dbAlias,
        }).then((res) => {
          this.tableNameList = []
          // console.log(this.tableNameList)
          if (res.code === 200) {
            this.tableNameList = res.data
            console.log(this.tableNameList)
          }
        })
      }
    },
    dbAliasChange(item) {
      // console.log(this.ruleForm.dbAlias)
      // this.$set(this.ruleForm, this.ruleForm.dbAlias, item)
      this.queryTableNameList()
      this.$nextTick(() => {
        this.$forceUpdate()
      })
    },
    queryAppNameDBAlias() {
      if (
        this.ruleForm.validationAppName !== '' &&
        this.ruleForm.validationAppName !== null &&
        this.ruleForm.validationAppName !== '--'
      ) {
        queryDbAliasByAppName({
          app_name: this.ruleForm.validationAppName,
        }).then((res) => {
          this.appAlias = []
          if (res.code === 200) {
            console.log(res.data)
            this.appAlias = res.data
          }
        })
      }
    },
    queryAllApps() {
      // this.apps = this.defaultApps
      const loadingInstance = ElLoading.service({
        fullscreen: true,
        background: 'rgba(0, 0, 0, 0.8)',
        spinner: 'el-icon-loading',
        text: '正在努力加载',
      })
      findAllAppInfo().then((response) => {
        this.lockButton = false
        loadingInstance.close()
        if (response.code === 200) {
          this.apps = response.data
        } else {
          this.$message.error(response.message)
        }
      }).catch(() => {
        loadingInstance.close()
      })
    },
    show(ruleForm = {}) {
      console.log('show方法接收到的参数:', ruleForm)

      this.title = this.titleDict[ruleForm['operationType']]
      console.log(this.title)

      if (JSON.stringify(ruleForm) === '{}') {
        this.ruleForm = { ...this.defaultRuleForm }
      } else {
        // 合并传入的参数到默认表单
        this.ruleForm = { ...this.defaultRuleForm, ...ruleForm }
      }

      console.log('合并后的ruleForm:', this.ruleForm)

      this.dialogVisible = true
      this.queryAllApps()

      // 统一处理所有操作类型
      if (this.ruleForm.appName !== undefined) {
        this.ruleForm.validationAppName = this.ruleForm.appName
        this.getVersionListByAppName(this.ruleForm.appName)
      }
      this.queryAppNameDBAlias()
      this.queryTableNameList()
    },
    close() {
      this.dialogVisible = false
      this.ruleForm = {
        validationSetName: '',
        appName: '',
        dbAlias: '',
        tableName: '',
        copyValidationSetId: 0,
      }
    },
    ok(formName) {
      // 如果是批量修改表名，检查表名是否有变化
      if (this.ruleForm.operationType === 'updateTableName') {
        if (!this.ruleForm.tableName) {
          this.$message.warning('请选择表名')
          return
        }
        // 直接调用保存方法，不需要表单验证
        this.saveOrUpdate()
        return
      }

      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.saveOrUpdate()
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    saveOrUpdate() {
      // 检查表名是否有变化
      if (this.ruleForm.operationType === 'updateTableName') {
        if (!this.ruleForm.tableName) {
          this.$message.warning('请选择新表名')
          return
        }
        if (this.ruleForm.oldTableName === this.ruleForm.tableName) {
          this.$message.warning('新表名不能与原表名相同')
          return
        }

        // 批量修改表名使用专门的接口，传递条件参数和原表名
        const params = {
          tableName: this.ruleForm.tableName,  // 新表名
          oldTableName: this.ruleForm.oldTableName,  // 原表名
          appName: this.ruleForm.validationAppName,
          validationAppName: this.ruleForm.validationAppName,
          dbAlias: this.ruleForm.dbAlias,
          caseVersion: this.ruleForm.caseVersion,
          caseAppName: this.ruleForm.appName
        }

        console.log('提交时的ruleForm:', this.ruleForm)
        console.log('提交的params:', params)

        this.dialogVisible = false
        batchUpdateTableNameApi(params)
          .then((res) => {
            if (res.code === 200) {
              this.$message({
                message: '批量修改表名成功',
                type: 'success',
                duration: 5000,
              })
              // 通知父组件刷新列表
              this.$emit('refresh')
            } else {
              this.$message({
                message: res.message,
                type: 'error',
                duration: 5000,
              })
            }
          })
          .catch((err) => {
            this.$message({
              message: err,
              type: 'error',
              duration: 5000,
            })
          })
      } else {
        // 其他操作类型使用原来的接口
        this.ruleForm.status = 'enable'
        this.dialogVisible = false
        batchOperateValidationSetDataApi(this.ruleForm)
          .then((res) => {
            if (res.code === 200) {
              this.$message({
                message: '批量操作成功',
                type: 'success',
                duration: 5000,
              })
              // 通知父组件刷新列表
              this.$emit('refresh')
            } else {
              this.$message({
                message: res.message,
                type: 'error',
                duration: 5000,
              })
            }
          })
          .catch((err) => {
            this.$message({
              message: err,
              type: 'error',
              duration: 5000,
            })
          })
      }
    },
  },
}
</script>

<style scoped>
.el-dialog__header {
  padding: 20px 20px 10px !important;
  border-bottom: 1px solid #ebebeb !important;
}
</style>
